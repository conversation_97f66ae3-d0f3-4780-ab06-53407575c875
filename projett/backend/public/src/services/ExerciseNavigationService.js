/**
 * Service de navigation intelligente pour les exercices
 * Traite l'intention voir_exercices avec correspondance sémantique et navigation
 */

const db = require('../models');
const { OpenAI } = require('openai');
const { Op } = require('sequelize');

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Charge les données complètes des exercices pour un niveau donné
 * @param {number} levelId - ID du niveau
 * @param {Array} matiereIds - IDs des matières autorisées pour ce niveau
 * @returns {Promise<Array>} - Liste des exercices avec leurs données complètes
 */
async function loadCompleteExercisesData(levelId, matiereIds) {
    try {
        console.log(`📚 Chargement des exercices pour niveau ${levelId}, matières [${matiereIds.join(', ')}]`);

        // Mapping des niveaux vers material_ids (même logique que manuelService.js)
        const manualLevelMapping = {
            6: [1, 1, 2],
            7: [3, 4, 4],
            8: [5, 5, 6, 7, 7, 8],
            9: [9, 9, 10, 11, 11, 12, 12],
            10: [13, 14, 14, 15, 16, 16, 17, 17],
            11: [18, 18, 19, 20, 21, 21, 22, 22, 23]
        };

        const levelMaterialIds = manualLevelMapping[levelId];
        if (!levelMaterialIds) {
            console.log(`⚠️ Niveau ${levelId} non supporté`);
            return [];
        }

        // Intersection entre les matières du niveau et les matières demandées
        const targetMaterialIds = levelMaterialIds.filter(id => matiereIds.includes(id));

        if (targetMaterialIds.length === 0) {
            console.log(`⚠️ Aucune matière correspondante pour niveau ${levelId}`);
            return [];
        }

        console.log(`🔄 Niveau ${levelId} -> material_ids [${targetMaterialIds.join(', ')}]`);

        // Récupérer tous les manuels pour ces material_ids
        const manuels = await db.Manuel.findAll({
            where: {
                material_id: { [Op.in]: targetMaterialIds }
            },
            attributes: ['id', 'name', 'material_id', 'logo'],
            include: [
                {
                    model: db.Material,
                    as: 'material',
                    attributes: ['id', 'name']
                }
            ]
        });

        if (manuels.length === 0) {
            console.log('⚠️ Aucun manuel trouvé pour ce niveau et ces matières');
            return [];
        }

        const manuelIds = manuels.map(m => m.id);
        console.log(`📖 ${manuels.length} manuels trouvés: [${manuelIds.join(', ')}]`);

        // Récupérer tous les exercices (vidéos) pour ces manuels
        const exercises = await db.Video.findAll({
            where: {
                manuel_id: { [Op.in]: manuelIds },
                status: 'APPROVED'
            },
            include: [
                {
                    model: db.User,
                    as: 'teacher',
                    attributes: ['id', 'full_name', 'avatar']
                },
                {
                    model: db.Manuel,
                    as: 'manuel',
                    attributes: ['id', 'name'],
                    include: [
                        {
                            model: db.Material,
                            as: 'material',
                            attributes: ['id', 'name']
                        }
                    ]
                }
            ],
            order: [['page', 'ASC'], ['numero', 'ASC']]
        });

        console.log(`🎯 ${exercises.length} exercices trouvés`);
        return exercises;
        
    } catch (error) {
        console.error('❌ Erreur lors du chargement des exercices:', error);
        return [];
    }
}

/**
 * Structure les données des exercices pour l'analyse GPT
 * @param {Array} exercises - Liste des exercices de la base de données
 * @returns {Array} - Données structurées pour GPT
 */
function structureExerciseDataForGPT(exercises) {
    return exercises.map(exercise => {
        return {
            id: exercise.id,
            titre: exercise.titre || '',
            description: exercise.description || '',
            page: exercise.page || null,
            numero: exercise.numero || null,
            manuel_name: exercise.manuel?.name || '',
            matiere: exercise.manuel?.material?.name || '',
            enseignant: exercise.teacher?.full_name || ''
        };
    });
}

/**
 * Utilise GPT pour trouver les exercices correspondant au message utilisateur
 * @param {string} userMessage - Message de l'utilisateur
 * @param {Array} structuredExercises - Exercices structurés pour GPT
 * @returns {Promise<Object>} - Résultat de l'analyse GPT
 */
async function findMatchingExercisesWithGPT(userMessage, structuredExercises) {
    const prompt = `Tu es un assistant éducatif intelligent spécialisé dans la correspondance d'exercices. Analyse le message de l'utilisateur et trouve les exercices qui correspondent le mieux à sa demande.

MESSAGE UTILISATEUR: "${userMessage}"

EXERCICES DISPONIBLES:
${JSON.stringify(structuredExercises, null, 2)}

INSTRUCTIONS:
1. Compare le message utilisateur avec les titres, descriptions, pages, numéros et contenus des exercices
2. Recherche des correspondances sémantiques (pas seulement exactes)
3. Considère les synonymes et les termes liés en arabe et français
4. Si l'utilisateur mentionne un numéro d'exercice spécifique, privilégie ce critère
5. Si l'utilisateur mentionne une page spécifique, privilégie les exercices de cette page
6. Évalue la pertinence de chaque exercice sur une échelle de 0 à 1
7. Retourne UNIQUEMENT un JSON avec cette structure exacte:

{
  "correspondances": [
    {
      "exercice_id": 123,
      "score_confiance": 0.95,
      "raison": "L'exercice correspond parfaitement car il s'agit de l'exercice numéro 5 page 12 demandé par l'utilisateur"
    }
  ],
  "aucune_correspondance": false
}

Si aucun exercice ne correspond (score < 0.3), retourne:
{
  "correspondances": [],
  "aucune_correspondance": true,
  "suggestion": "Aucun exercice trouvé correspondant à ta demande. Essaie de préciser le numéro d'exercice ou la page."
}

IMPORTANT: 
- Retourne UNIQUEMENT le JSON, rien d'autre
- Trie les correspondances par score décroissant
- Inclus seulement les exercices avec un score >= 0.3
- Privilégie les correspondances exactes de numéro et page

RÉPONSE (JSON uniquement):`;

    try {
        console.log('🤖 Appel GPT pour correspondance d\'exercices...');
        const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [{ role: "user", content: prompt }],
            temperature: 0.3,
            max_tokens: 1000
        });

        const gptResponse = completion.choices[0].message.content.trim();
        console.log('🤖 Réponse GPT brute:', gptResponse);
        
        // Nettoyer la réponse pour extraire le JSON
        const jsonMatch = gptResponse.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('Aucun JSON trouvé dans la réponse GPT');
        }
        
        const cleanedResponse = jsonMatch[0];
        const parsedResponse = JSON.parse(cleanedResponse);
        
        console.log('🤖 Réponse GPT parsée:', JSON.stringify(parsedResponse, null, 2));
        return parsedResponse;
        
    } catch (error) {
        console.error('❌ Erreur GPT:', error);
        return { 
            correspondances: [], 
            aucune_correspondance: true,
            suggestion: "Erreur lors de l'analyse. Essaie de reformuler ta demande."
        };
    }
}

/**
 * Traite l'intention voir_exercices avec navigation intelligente
 * @param {number} levelId - ID du niveau
 * @param {string} matiereName - Nom de la matière
 * @param {string} userMessage - Message de l'utilisateur
 * @param {Array} matiereIds - IDs des matières autorisées pour ce niveau
 * @returns {Promise<Object>} - Résultat avec réponse et navigation
 */
async function processVoirExercicesIntention(levelId, matiereName, userMessage, matiereIds) {
    try {
        console.log(`🎯 Traitement intention voir_exercices: niveau=${levelId}, matière=${matiereName}, message="${userMessage}"`);
        
        // 1. Charger les données complètes des exercices
        const exercises = await loadCompleteExercisesData(levelId, matiereIds);
        
        if (exercises.length === 0) {
            return {
                success: false,
                response: "لا توجد تمارين متاحة لمستواك حالياً.",
                responseAr: "لا توجد تمارين متاحة لمستواك حالياً.",
                responseFr: "Aucun exercice n'est disponible pour votre niveau actuellement.",
                navigation: null
            };
        }
        
        // 2. Si pas de message spécifique, retourner tous les exercices
        if (!userMessage || userMessage.trim() === "") {
            const exerciseList = exercises.map(exercise => {
                return {
                    id: exercise.id,
                    title: exercise.titre || 'تمرين بدون عنوان',
                    page: exercise.page,
                    numero: exercise.numero,
                    manuel: exercise.manuel?.name || 'كتاب غير معروف',
                    teacher: exercise.teacher?.full_name || 'مدرس غير معروف',
                    thumbnail: exercise.thumbnail
                };
            });
            
            return {
                success: true,
                response: `إليك ${exercises.length} تمارين متاحة لمستواك:`,
                responseAr: `إليك ${exercises.length} تمارين متاحة لمستواك:`,
                responseFr: `Voici les ${exercises.length} exercices disponibles pour ton niveau :`,
                data: {
                    exercises: exerciseList
                }
            };
        }
        
        // 3. Structurer les données pour GPT
        const structuredExercises = structureExerciseDataForGPT(exercises);
        
        // 4. Utiliser GPT pour trouver les correspondances
        const gptResult = await findMatchingExercisesWithGPT(userMessage, structuredExercises);
        
        // 5. Traiter les résultats
        if (gptResult.aucune_correspondance || !gptResult.correspondances || gptResult.correspondances.length === 0) {
            return {
                success: false,
                response: gptResult.suggestion || "لم أجد أي تمارين تطابق طلبك.",
                responseAr: "لم أجد أي تمارين تطابق طلبك. جرب إعادة صياغة السؤال.",
                responseFr: "Aucun exercice trouvé correspondant à ta demande. Essaie de reformuler.",
                navigation: null
            };
        }
        
        // 6. Si un seul exercice correspond avec un score élevé (>= 0.8), navigation directe
        if (gptResult.correspondances.length === 1 && gptResult.correspondances[0].score_confiance >= 0.8) {
            const matchedExercise = exercises.find(e => e.id === gptResult.correspondances[0].exercice_id);
            if (matchedExercise) {
                const title = matchedExercise.titre || 'تمرين بدون عنوان';

                // Préparer les données de l'exercice pour la carte
                const exerciseData = {
                    id: matchedExercise.id,
                    title: title,
                    description: matchedExercise.description || '',
                    page: matchedExercise.page,
                    numero: matchedExercise.numero,
                    thumbnail: matchedExercise.thumbnail,
                    video: matchedExercise.video,
                    manuel_name: matchedExercise.manuel?.name || '',
                    teacher: {
                        id: matchedExercise.teacher?.id,
                        name: matchedExercise.teacher?.full_name,
                        avatar: matchedExercise.teacher?.avatar
                    },
                    score: gptResult.correspondances[0].score_confiance
                };

                return {
                    success: true,
                    response: `ممتاز! سأوجهك إلى التمرين "${title}".`,
                    responseAr: `ممتاز! سأوجهك إلى التمرين "${title}".`,
                    responseFr: `Parfait ! Je t'emmène vers l'exercice "${title}".`,
                    navigation: {
                        screen: 'ExerciseDetailScreen',
                        params: { exerciseId: matchedExercise.id }
                    },
                    exerciseData: exerciseData
                };
            }
        }
        
        // 7. Plusieurs exercices correspondent, proposer une liste
        const matchingExercises = gptResult.correspondances.map(match => {
            const exercise = exercises.find(e => e.id === match.exercice_id);
            if (exercise) {
                const title = exercise.titre || 'تمرين بدون عنوان';
                return {
                    id: exercise.id,
                    title: title,
                    page: exercise.page,
                    numero: exercise.numero,
                    manuel: exercise.manuel?.name || 'كتاب غير معروف',
                    teacher: exercise.teacher?.full_name || 'مدرس غير معروف',
                    thumbnail: exercise.thumbnail,
                    score: match.score_confiance,
                    raison: match.raison
                };
            }
            return null;
        }).filter(Boolean);
        
        return {
            success: true,
            response: `وجدت ${matchingExercises.length} تمارين تطابق طلبك. اختر واحداً منها:`,
            responseAr: `وجدت ${matchingExercises.length} تمارين تطابق طلبك. اختر واحداً منها:`,
            responseFr: `J'ai trouvé ${matchingExercises.length} exercices correspondant à ta demande. Choisis-en un :`,
            data: {
                exercises: matchingExercises
            }
        };
        
    } catch (error) {
        console.error('❌ Erreur dans processVoirExercicesIntention:', error);
        return {
            success: false,
            response: "عذراً، حدث خطأ أثناء البحث عن التمارين.",
            responseAr: "عذراً، حدث خطأ أثناء البحث عن التمارين.",
            responseFr: "Désolé, une erreur s'est produite lors de la recherche des exercices.",
            navigation: null
        };
    }
}

module.exports = {
    processVoirExercicesIntention,
    loadCompleteExercisesData,
    structureExerciseDataForGPT,
    findMatchingExercisesWithGPT
};
