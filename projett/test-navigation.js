#!/usr/bin/env node

/**
 * Script de test pour vérifier la navigation automatique du chatbot
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_USER_ID = 3712; // ID utilisateur de test

async function testCourseNavigation() {
    console.log('🧪 Test de navigation automatique vers un cours');
    console.log('='.repeat(50));

    try {
        // Test avec une question qui devrait trouver un cours spécifique
        const testMessage = 'هل يوجد درس اسمه التدريب على المساءل؟';
        
        console.log(`📝 Message de test: "${testMessage}"`);
        console.log(`👤 ID utilisateur: ${TEST_USER_ID}`);
        
        const response = await axios.post(`${BASE_URL}/chatbot/ask`, {
            userId: TEST_USER_ID,
            message: testMessage,
            messageType: 'text'
        });

        console.log('\n📥 RÉPONSE REÇUE:');
        console.log('='.repeat(30));
        console.log(`💬 Réponse: ${response.data.reply}`);
        
        if (response.data.navigation) {
            console.log('\n🧭 DONNÉES DE NAVIGATION:');
            console.log(`📱 Screen: ${response.data.navigation.screen}`);
            console.log(`🔗 Params:`, JSON.stringify(response.data.navigation.params, null, 2));

            if (response.data.navigation.screen === 'WebinarDetailScreen') {
                console.log('\n✅ NAVIGATION AUTOMATIQUE ATTENDUE');
                console.log(`🎯 Le frontend devrait naviguer vers WebinarDetail avec webinarId: ${response.data.navigation.params?.webinarId}`);

                // Vérifier les données du cours pour la carte
                if (response.data.courseData) {
                    console.log('\n🎴 CARTE DE COURS ATTENDUE:');
                    console.log(`📚 Titre: ${response.data.courseData.title}`);
                    console.log(`👨‍🏫 Enseignant: ${response.data.courseData.teacher?.name || response.data.courseData.teacher?.full_name}`);
                    console.log(`💰 Prix: ${response.data.courseData.price || 0} د.ت`);
                    console.log(`⏱️ Durée: ${response.data.courseData.duration || 'Non spécifiée'} min`);
                    console.log(`🎯 Score: ${response.data.courseData.score || 'N/A'}`);
                    console.log('✅ Une carte de cours sera affichée pour permettre le retour facile');
                } else {
                    console.log('\n⚠️ DONNÉES DE COURS MANQUANTES');
                    console.log('❌ Aucune carte de cours ne sera affichée');
                }
            } else {
                console.log('\n📋 AFFICHAGE DE LISTE ATTENDU');
                console.log('👆 L\'utilisateur devra choisir dans la liste affichée');
            }
        } else {
            console.log('\n❌ AUCUNE DONNÉE DE NAVIGATION');
            console.log('⚠️ Le chatbot n\'a pas trouvé de cours correspondant');
        }

        console.log('\n🎵 AUDIO:');
        console.log(`🔊 Audio disponible: ${response.data.audio ? 'Oui' : 'Non'}`);
        if (response.data.audio) {
            console.log(`🎤 Chemin audio: ${response.data.audio}`);
        }

    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        if (error.response) {
            console.error('📄 Détails de l\'erreur:', error.response.data);
        }
    }
}

// Exécuter le test
testCourseNavigation();
